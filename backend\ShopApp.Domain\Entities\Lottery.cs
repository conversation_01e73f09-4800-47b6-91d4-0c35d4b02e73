using System;
using System.Collections.Generic;
using ShopApp.Core.Common;
using ShopApp.Core.Identity;

namespace ShopApp.Domain.Entities;

public class Lottery : BaseEntity
{
    public string Title { get; set; }
    public string Description { get; set; }
    public string Prize { get; set; }
    public decimal TicketPrice { get; set; }
    public int MaxTickets { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public LotteryStatus Status { get; set; } = LotteryStatus.Active;
    public string? PrizeImageUrl { get; set; }
    public string? PrizeImageAlt { get; set; }
    public DateTime? DrawDate { get; set; }
    public Guid? WinnerId { get; set; }
    public User? Winner { get; set; }

    // Navigation properties
    public ICollection<LotteryTicket> Tickets { get; set; } = new List<LotteryTicket>();
}

public class LotteryTicket : BaseEntity
{
    public Guid LotteryId { get; set; }
    public Lottery Lottery { get; set; }
    public Guid UserId { get; set; }
    public User User { get; set; }
    public string TicketNumber { get; set; }
    public decimal PaidAmount { get; set; }
    public bool IsWinner { get; set; } = false;
}

public enum LotteryStatus
{
    Active = 0,
    Completed = 1,
    Cancelled = 2,
    Draft = 3
}
