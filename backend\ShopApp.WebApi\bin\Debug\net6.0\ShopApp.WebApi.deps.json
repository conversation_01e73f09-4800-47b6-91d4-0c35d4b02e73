{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"ShopApp.WebApi/1.0.0": {"dependencies": {"AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "Microsoft.AspNetCore.Authentication.JwtBearer": "6.0.36", "Microsoft.EntityFrameworkCore.Tools": "6.0.36", "Serilog.Sinks.Console": "5.0.1", "Serilog.Sinks.File": "5.0.0", "ShopApp.Application": "1.0.0", "ShopApp.Core": "1.0.0", "ShopApp.Domain": "1.0.0", "ShopApp.Persistence": "1.0.0", "Swashbuckle.AspNetCore": "6.5.0"}, "runtime": {"ShopApp.WebApi.dll": {}}}, "AutoMapper/12.0.1": {"runtime": {"lib/netstandard2.1/AutoMapper.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.1.0"}}}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"dependencies": {"AutoMapper": "12.0.1"}, "runtime": {"lib/netstandard2.1/AutoMapper.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.0.1.0"}}}, "FluentValidation/11.8.0": {"runtime": {"lib/net6.0/FluentValidation.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.8.0.0"}}}, "FluentValidation.DependencyInjectionExtensions/11.8.0": {"dependencies": {"FluentValidation": "11.8.0"}, "runtime": {"lib/netstandard2.1/FluentValidation.DependencyInjectionExtensions.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.8.0.0"}}}, "Humanizer.Core/2.8.26": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.26.1919"}}}, "MediatR/12.5.0": {"dependencies": {"MediatR.Contracts": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/net6.0/MediatR.dll": {"assemblyVersion": "12.0.0.0", "fileVersion": "12.5.0.0"}}}, "MediatR.Contracts/2.0.1": {"runtime": {"lib/netstandard2.0/MediatR.Contracts.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "MediatR.Extensions.Microsoft.DependencyInjection/11.1.0": {"dependencies": {"MediatR": "12.5.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "runtime": {"lib/netstandard2.1/MediatR.Extensions.Microsoft.DependencyInjection.dll": {"assemblyVersion": "11.0.0.0", "fileVersion": "11.1.0.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.36": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.36.0"}, "runtime": {"lib/net6.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51604"}}}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Bcl.Memory/9.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Bcl.TimeProvider/8.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Bcl.TimeProvider.dll": {"assemblyVersion": "8.0.0.1", "fileVersion": "8.0.123.58001"}}}, "Microsoft.Data.SqlClient/2.1.7": {"dependencies": {"Microsoft.Data.SqlClient.SNI.runtime": "2.1.1", "Microsoft.Identity.Client": "4.21.1", "Microsoft.IdentityModel.JsonWebTokens": "8.9.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.36.0", "System.Configuration.ConfigurationManager": "4.7.0", "System.Runtime.Caching": "4.7.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "unix", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}, "runtimes/win/lib/netcoreapp3.1/Microsoft.Data.SqlClient.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "2.0.20168.4", "fileVersion": "2.0.20168.4"}}}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"runtimeTargets": {"runtimes/win-arm/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "2.1.1.0"}, "runtimes/win-arm64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "2.1.1.0"}, "runtimes/win-x64/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "2.1.1.0"}, "runtimes/win-x86/native/Microsoft.Data.SqlClient.SNI.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "2.1.1.0"}}}, "Microsoft.Data.Sqlite.Core/6.0.36": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0/Microsoft.Data.Sqlite.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "6.0.36", "Microsoft.EntityFrameworkCore.Analyzers": "6.0.36"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.36": {"runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.36": {}, "Microsoft.EntityFrameworkCore.Design/6.0.36": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.EntityFrameworkCore.Relational": "6.0.36"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Relational/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.36"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Sqlite/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore.Sqlite.Core": "6.0.36", "SQLitePCLRaw.bundle_e_sqlite3": "2.1.2"}}, "Microsoft.EntityFrameworkCore.Sqlite.Core/6.0.36": {"dependencies": {"Microsoft.Data.Sqlite.Core": "6.0.36", "Microsoft.EntityFrameworkCore.Relational": "6.0.36", "Microsoft.Extensions.DependencyModel": "6.0.2"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.Sqlite.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.36": {"dependencies": {"Microsoft.Data.SqlClient": "2.1.7", "Microsoft.EntityFrameworkCore.Relational": "6.0.36", "Microsoft.IdentityModel.JsonWebTokens": "8.9.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.36.0"}, "runtime": {"lib/net6.0/Microsoft.EntityFrameworkCore.SqlServer.dll": {"assemblyVersion": "********", "fileVersion": "6.0.3624.51504"}}}, "Microsoft.EntityFrameworkCore.Tools/6.0.36": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "6.0.36"}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Binder.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"runtime": {"lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "Microsoft.Extensions.DependencyModel/6.0.2": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyModel.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.3624.51421"}}}, "Microsoft.Extensions.Primitives/9.0.4": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.425.16305"}}}, "Microsoft.Identity.Client/4.21.1": {"runtime": {"lib/netcoreapp2.1/Microsoft.Identity.Client.dll": {"assemblyVersion": "4.21.1.0", "fileVersion": "4.21.1.0"}}}, "Microsoft.IdentityModel.Abstractions/8.9.0": {"runtime": {"lib/net6.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "8.9.0.0", "fileVersion": "8.9.0.60424"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.9.0": {"dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.9.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "8.9.0.0", "fileVersion": "8.9.0.60424"}}}, "Microsoft.IdentityModel.Logging/8.9.0": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.9.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "8.9.0.0", "fileVersion": "8.9.0.60424"}}}, "Microsoft.IdentityModel.Protocols/6.36.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.9.0", "Microsoft.IdentityModel.Tokens": "8.9.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "6.36.0.0", "fileVersion": "6.36.0.50718"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.36.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "6.36.0", "System.IdentityModel.Tokens.Jwt": "8.9.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "6.36.0.0", "fileVersion": "6.36.0.50718"}}}, "Microsoft.IdentityModel.Tokens/8.9.0": {"dependencies": {"Microsoft.Bcl.Memory": "9.0.0", "Microsoft.Bcl.TimeProvider": "8.0.1", "Microsoft.IdentityModel.Logging": "8.9.0"}, "runtime": {"lib/net6.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "8.9.0.0", "fileVersion": "8.9.0.60424"}}}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "1.2.3.0", "fileVersion": "1.2.3.0"}}}, "Npgsql/6.0.11": {"runtime": {"lib/net6.0/Npgsql.dll": {"assemblyVersion": "6.0.11.0", "fileVersion": "6.0.11.0"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.29": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.36", "Microsoft.EntityFrameworkCore.Abstractions": "6.0.36", "Microsoft.EntityFrameworkCore.Relational": "6.0.36", "Npgsql": "6.0.11"}, "runtime": {"lib/net6.0/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "6.0.29.0", "fileVersion": "6.0.29.0"}}}, "Serilog/3.1.1": {"runtime": {"lib/net6.0/Serilog.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "3.1.1.0"}}}, "Serilog.Sinks.Console/5.0.1": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net6.0/Serilog.Sinks.Console.dll": {"assemblyVersion": "5.0.1.0", "fileVersion": "5.0.1.0"}}}, "Serilog.Sinks.File/5.0.0": {"dependencies": {"Serilog": "3.1.1"}, "runtime": {"lib/net5.0/Serilog.Sinks.File.dll": {"assemblyVersion": "5.0.0.0", "fileVersion": "5.0.0.0"}}}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.lib.e_sqlite3": "2.1.2", "SQLitePCLRaw.provider.e_sqlite3": "2.1.2"}, "runtime": {"lib/netstandard2.0/SQLitePCLRaw.batteries_v2.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.core/2.1.2": {"runtime": {"lib/netstandard2.0/SQLitePCLRaw.core.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"runtimeTargets": {"runtimes/alpine-arm/native/libe_sqlite3.so": {"rid": "alpine-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-arm64/native/libe_sqlite3.so": {"rid": "alpine-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/alpine-x64/native/libe_sqlite3.so": {"rid": "alpine-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/browser-wasm/nativeassets/net6.0/e_sqlite3.a": {"rid": "browser-wasm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm/native/libe_sqlite3.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libe_sqlite3.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libe_sqlite3.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-mips64/native/libe_sqlite3.so": {"rid": "linux-mips64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm/native/libe_sqlite3.so": {"rid": "linux-musl-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-arm64/native/libe_sqlite3.so": {"rid": "linux-musl-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libe_sqlite3.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-s390x/native/libe_sqlite3.so": {"rid": "linux-s390x", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libe_sqlite3.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x86/native/libe_sqlite3.so": {"rid": "linux-x86", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-arm64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/maccatalyst-x64/native/libe_sqlite3.dylib": {"rid": "maccatalyst-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-arm64/native/libe_sqlite3.dylib": {"rid": "osx-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libe_sqlite3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/e_sqlite3.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm64/native/e_sqlite3.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/e_sqlite3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/e_sqlite3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"dependencies": {"SQLitePCLRaw.core": "2.1.2"}, "runtime": {"lib/net6.0/SQLitePCLRaw.provider.e_sqlite3.dll": {"assemblyVersion": "2.1.2.1721", "fileVersion": "2.1.2.1721"}}}, "Swashbuckle.AspNetCore/6.5.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "6.5.0", "Swashbuckle.AspNetCore.SwaggerGen": "6.5.0", "Swashbuckle.AspNetCore.SwaggerUI": "6.5.0"}}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.5.0"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Configuration.ConfigurationManager/4.7.0": {"dependencies": {"System.Security.Cryptography.ProtectedData": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.19.56404"}}}, "System.IdentityModel.Tokens.Jwt/8.9.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.9.0", "Microsoft.IdentityModel.Tokens": "8.9.0"}, "runtime": {"lib/net6.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "8.9.0.0", "fileVersion": "8.9.0.60424"}}}, "System.Runtime.Caching/4.7.0": {"dependencies": {"System.Configuration.ConfigurationManager": "4.7.0"}, "runtime": {"lib/netstandard2.0/System.Runtime.Caching.dll": {"assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Runtime.Caching.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.1.0", "fileVersion": "4.700.19.56404"}}}, "System.Security.Cryptography.ProtectedData/4.7.0": {"runtime": {"lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/System.Security.Cryptography.ProtectedData.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "4.0.5.0", "fileVersion": "4.700.19.56404"}}}, "ShopApp.Application/1.0.0": {"dependencies": {"AutoMapper": "12.0.1", "AutoMapper.Extensions.Microsoft.DependencyInjection": "12.0.1", "FluentValidation": "11.8.0", "FluentValidation.DependencyInjectionExtensions": "11.8.0", "MediatR": "12.5.0", "MediatR.Extensions.Microsoft.DependencyInjection": "11.1.0", "Microsoft.EntityFrameworkCore": "6.0.36", "ShopApp.Core": "1.0.0", "ShopApp.Domain": "1.0.0"}, "runtime": {"ShopApp.Application.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "ShopApp.Core/1.0.0": {"dependencies": {"FluentValidation": "11.8.0", "Microsoft.EntityFrameworkCore": "6.0.36", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.IdentityModel.Tokens": "8.9.0", "Serilog": "3.1.1", "Serilog.Sinks.File": "5.0.0", "System.IdentityModel.Tokens.Jwt": "8.9.0"}, "runtime": {"ShopApp.Core.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "ShopApp.Domain/1.0.0": {"dependencies": {"ShopApp.Core": "1.0.0"}, "runtime": {"ShopApp.Domain.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "ShopApp.Persistence/1.0.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "6.0.36", "Microsoft.EntityFrameworkCore.SqlServer": "6.0.36", "Microsoft.EntityFrameworkCore.Sqlite": "6.0.36", "Npgsql.EntityFrameworkCore.PostgreSQL": "6.0.29", "ShopApp.Application": "1.0.0", "ShopApp.Core": "1.0.0", "ShopApp.Domain": "1.0.0"}, "runtime": {"ShopApp.Persistence.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}}}, "libraries": {"ShopApp.WebApi/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AutoMapper/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-hvV62vl6Hp/WfQ24yzo3Co9+OPl8wH8hApwVtgWpiAynVJkUcs7xvehnSftawL8Pe8FrPffBRM3hwzLQqWDNjA==", "path": "automapper/12.0.1", "hashPath": "automapper.12.0.1.nupkg.sha512"}, "AutoMapper.Extensions.Microsoft.DependencyInjection/12.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-+g/K+Vpe3gGMKGzjslMOdqNlkikScDjWfVvmWTayrDHaG/n2pPmFBMa+jKX1r/h6BDGFdkyRjAuhFE3ykW+r1g==", "path": "automapper.extensions.microsoft.dependencyinjection/12.0.1", "hashPath": "automapper.extensions.microsoft.dependencyinjection.12.0.1.nupkg.sha512"}, "FluentValidation/11.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-TiEaaYqSIAS+wXQv46LRm8Mca092XYNePD0n231Hqlkmm5FuToZoWnjI1ef/vJUxBzqR7/k2vaVQXxijNgVKRQ==", "path": "fluentvalidation/11.8.0", "hashPath": "fluentvalidation.11.8.0.nupkg.sha512"}, "FluentValidation.DependencyInjectionExtensions/11.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-fNfH9XEwevXb1JKCWGEkmkIKDYK3+gaAG8ugeOggd0oc8hdwsl3SNC59qAM1tcKVUhcD+AEDCI4hXX5WWshpWg==", "path": "fluentvalidation.dependencyinjectionextensions/11.8.0", "hashPath": "fluentvalidation.dependencyinjectionextensions.11.8.0.nupkg.sha512"}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "MediatR/12.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-vqm2H8/nqL5NAJHPhsG1JOPwfkmbVrPyh4svdoRzu+uZh6Ex7PRoHBGsLYC0/RWCEJFqD1ohHNpteQvql9OktA==", "path": "mediatr/12.5.0", "hashPath": "mediatr.12.5.0.nupkg.sha512"}, "MediatR.Contracts/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-FYv95bNT4UwcNA+G/J1oX5OpRiSUxteXaUt2BJbRSdRNiIUNbggJF69wy6mnk2wYToaanpdXZdCwVylt96MpwQ==", "path": "mediatr.contracts/2.0.1", "hashPath": "mediatr.contracts.2.0.1.nupkg.sha512"}, "MediatR.Extensions.Microsoft.DependencyInjection/11.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-RW3etRuy6Xp63cgqfC0r/ITOtUT0f9ymJl1+1XZdzsYUsfv7WBPC2kXGOP8IYpY/6c4Q6805vbIW/88DopUs3w==", "path": "mediatr.extensions.microsoft.dependencyinjection/11.1.0", "hashPath": "mediatr.extensions.microsoft.dependencyinjection.11.1.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-vGDqvZUehbtKdoUzVO32N0Lxon+1piGO6qFnA8FNXEzWhrLN5uum8ZyhWgrWYSA4b6JuCBJ/wAC7rV38wBvLew==", "path": "microsoft.aspnetcore.authentication.jwtbearer/6.0.36", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.6.0.36.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw==", "path": "microsoft.bcl.asyncinterfaces/8.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.8.0.0.nupkg.sha512"}, "Microsoft.Bcl.Memory/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bTUtGfpGyJnohQzjdXbtc7MqNzkv7CWUSRz54+ucNm0i32rZiIU0VdVPHDBShOl1qhVKRjW8mnEBz3d2vH93tQ==", "path": "microsoft.bcl.memory/9.0.0", "hashPath": "microsoft.bcl.memory.9.0.0.nupkg.sha512"}, "Microsoft.Bcl.TimeProvider/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-C7kWHJnMRY7EvJev2S8+yJHZ1y7A4ZlLbA4NE+O23BDIAN5mHeqND1m+SKv1ChRS5YlCDW7yAMUe7lttRsJaAA==", "path": "microsoft.bcl.timeprovider/8.0.1", "hashPath": "microsoft.bcl.timeprovider.8.0.1.nupkg.sha512"}, "Microsoft.Data.SqlClient/2.1.7": {"type": "package", "serviceable": true, "sha512": "sha512-dSdlcXPszeOjjDX9a0buMFgYqKrI5bTxdSgX3JyCa+OL80NUstJSxOJr0j9oOn8mpP5PgWeRC2bVf/Zf2Cjv+g==", "path": "microsoft.data.sqlclient/2.1.7", "hashPath": "microsoft.data.sqlclient.2.1.7.nupkg.sha512"}, "Microsoft.Data.SqlClient.SNI.runtime/2.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-JwGDWkyZgm7SATJmFLfT2G4teimvNbNtq3lsS9a5DzvhEZnQrZjZhevCU0vdx8MjheLHoG5vocuO03QtioFQxQ==", "path": "microsoft.data.sqlclient.sni.runtime/2.1.1", "hashPath": "microsoft.data.sqlclient.sni.runtime.2.1.1.nupkg.sha512"}, "Microsoft.Data.Sqlite.Core/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-Vh74dyfNQJBc58lcOuhSKH39AuQlgGatAY4bOCZx7F+8/GO3Ba2G9G1swCVhBQkeTG2Ftew8R6iga02ATZiE6w==", "path": "microsoft.data.sqlite.core/6.0.36", "hashPath": "microsoft.data.sqlite.core.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-0i1BBvJBrqdmIdTqCmL+/J74HucYqc5eck3J5trKe6AN2fvdE1lICto6HBwNhbtPniZO7bhW36FnIjTK0FamXg==", "path": "microsoft.entityframeworkcore/6.0.36", "hashPath": "microsoft.entityframeworkcore.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-nrR4lfe9izQK1eerKW/ECHuJV8xXtuvoj/APrwwOjX4+Ne2SMXBpetctPcYNVc3KyiKuUHJSLywWtsqoXE5ElA==", "path": "microsoft.entityframeworkcore.abstractions/6.0.36", "hashPath": "microsoft.entityframeworkcore.abstractions.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON><PERSON>wloBaAJcLwgSW4hvGdAWjKnuIwgQ2PKTNkvG80PW/WFgedwKomY9wuO5BPewIHlX6huGyP//StQQRQOWr+Q==", "path": "microsoft.entityframeworkcore.analyzers/6.0.36", "hashPath": "microsoft.entityframeworkcore.analyzers.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-r9dwblLaEPSdHocaouGINBxfqPJieOCs4Z44gMXuWBCrlnfuteoXGp3lSZXtifSZCLdOcFMoUHpDgpSrk9CDmg==", "path": "microsoft.entityframeworkcore.design/6.0.36", "hashPath": "microsoft.entityframeworkcore.design.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-Vq9UQEFNU1t42AOSgz6bUe0fpMa1g4wO8Y7EfhYPX6VSriRSRB4ImTC2TEZjOUeyGcZyUI2Kyd6//RfUPUR+Pw==", "path": "microsoft.entityframeworkcore.relational/6.0.36", "hashPath": "microsoft.entityframeworkcore.relational.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-NWlTJgKQRrZcdN+vph/PSZ1e3a9LDvBgiO/WQLfMWwWEv0XdixMRh6hWaKI+QpqdVCtwzZPQovEBzhJBdgIasA==", "path": "microsoft.entityframeworkcore.sqlite/6.0.36", "hashPath": "microsoft.entityframeworkcore.sqlite.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Sqlite.Core/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-LQjAvbbFD673QABvnJXmGERqEg8NCEimSTPm17jaUVwG38V+rppXZqPnXoKhfV8XrOD7yneJ3+/1vA+cqtk7PQ==", "path": "microsoft.entityframeworkcore.sqlite.core/6.0.36", "hashPath": "microsoft.entityframeworkcore.sqlite.core.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.SqlServer/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-kSpJFqRu8KM6J/C+Q93n5wcqVKt3cu9jRy0P1HlWm6RSQfCoEAyonwOaMmOctN+d8ooziUanTAFV63iQyww0Ug==", "path": "microsoft.entityframeworkcore.sqlserver/6.0.36", "hashPath": "microsoft.entityframeworkcore.sqlserver.6.0.36.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/6.0.36": {"type": "package", "serviceable": true, "sha512": "sha512-zBo/oepzDsoXCwfE4mwxzA3lBrYMtfpmkQ1rmtQnuvaORY6yCeVIx4Vpr/ENz3DShuHiNYr7c1ClBSZleW78Dw==", "path": "microsoft.entityframeworkcore.tools/6.0.36", "hashPath": "microsoft.entityframeworkcore.tools.6.0.36.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "path": "microsoft.extensions.configuration.abstractions/9.0.4", "hashPath": "microsoft.extensions.configuration.abstractions.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "path": "microsoft.extensions.configuration.binder/9.0.4", "hashPath": "microsoft.extensions.configuration.binder.9.0.4.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyModel/6.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-HS5YsudCGSVoCVdsYJ5FAO9vx0z04qSAXgVzpDJSQ1/w/X9q8hrQVGU2p+Yfui+2KcXLL+Zjc0SX3yJWtBmYiw==", "path": "microsoft.extensions.dependencymodel/6.0.2", "hashPath": "microsoft.extensions.dependencymodel.6.0.2.nupkg.sha512"}, "Microsoft.Extensions.Primitives/9.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA==", "path": "microsoft.extensions.primitives/9.0.4", "hashPath": "microsoft.extensions.primitives.9.0.4.nupkg.sha512"}, "Microsoft.Identity.Client/4.21.1": {"type": "package", "serviceable": true, "sha512": "sha512-vycgk7S/HAbHaUaK4Tid1fsWHsXdFRRP2KavAIOHCVV27zvuQfYAjXmMvctuuF4egydSumG58CwPZob3gWeYgQ==", "path": "microsoft.identity.client/4.21.1", "hashPath": "microsoft.identity.client.4.21.1.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-b/87S+lb86U7Ns7xgTKnqql6XGNr8hBE+k0rj5sRWwXeJe6uA+3mSjvpZ9GoQo3cB9zlwzcbGBU8KM44qX0t1g==", "path": "microsoft.identitymodel.abstractions/8.9.0", "hashPath": "microsoft.identitymodel.abstractions.8.9.0.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-QcNC57hJLc6LIcy2PTYlD8iRBQBm6bqPKbCjsRYWlp7QTyJisF0ImUWaa3mx6wWaS1upwYneYVPiIiNSlAy16g==", "path": "microsoft.identitymodel.jsonwebtokens/8.9.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.9.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswvH4ZANbFsJYEn+PGEOj7nkkBRjnsb7LcYGAS16VUJpSeKULLeYSy/7SK6jLO1WTT12xqdeL4mj3dYT7GdoQ==", "path": "microsoft.identitymodel.logging/8.9.0", "hashPath": "microsoft.identitymodel.logging.8.9.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/6.36.0": {"type": "package", "serviceable": true, "sha512": "sha512-ODK3Nr7PbTItXFavZ+tfNnBcBxb85v7OpXZc1l4s3OrmVLJzV7SHdqaAMOds6+5OYqt8UdOketAcPegTKj54ug==", "path": "microsoft.identitymodel.protocols/6.36.0", "hashPath": "microsoft.identitymodel.protocols.6.36.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/6.36.0": {"type": "package", "serviceable": true, "sha512": "sha512-nD1RNQjMYKgOL1Bp8pSWx7G6F6R/Rkq/fTw4epRjY8v2AYeXk0Fy6Gyt7k+JsPZ7ZJzkW9V9K+u3DPoz/oL5PQ==", "path": "microsoft.identitymodel.protocols.openidconnect/6.36.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.6.36.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-qK6kW5qZvDj7E5RLWQ9gzJxQe5GUz7+7bXrLQQydSDF9hTf5Ip2qHuAQW3Fg9GND6jkjTr7IXAZFmBHadNQi4Q==", "path": "microsoft.identitymodel.tokens/8.9.0", "hashPath": "microsoft.identitymodel.tokens.8.9.0.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Npgsql/6.0.11": {"type": "package", "serviceable": true, "sha512": "sha512-ZPpjFpW0m71pqAMIpUdVZUY74TCTqCXrytbFsMvm8IuAadpfV9K1lwphuyDBKCsc3w1i7TH+aevHQ6TZ+b0ZMw==", "path": "npgsql/6.0.11", "hashPath": "npgsql.6.0.11.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/6.0.29": {"type": "package", "serviceable": true, "sha512": "sha512-GCUgp/4ZltsysSRJUpvpQoFCRJ6OciL5/xZePOpZ8bekthAbLGW3emYaKf2V+1mNXUm9LhCZI7T0ib3I8yNV3A==", "path": "npgsql.entityframeworkcore.postgresql/6.0.29", "hashPath": "npgsql.entityframeworkcore.postgresql.6.0.29.nupkg.sha512"}, "Serilog/3.1.1": {"type": "package", "serviceable": true, "sha512": "sha512-P6G4/4Kt9bT635bhuwdXlJ2SCqqn2nhh4gqFqQueCOr9bK/e7W9ll/IoX1Ter948cV2Z/5+5v8pAfJYUISY03A==", "path": "serilog/3.1.1", "hashPath": "serilog.3.1.1.nupkg.sha512"}, "Serilog.Sinks.Console/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-6Jt8jl9y2ey8VV7nVEUAyjjyxjAQuvd5+qj4XYAT9CwcsvR70HHULGBeD+K2WCALFXf7CFsNQT4lON6qXcu2AA==", "path": "serilog.sinks.console/5.0.1", "hashPath": "serilog.sinks.console.5.0.1.nupkg.sha512"}, "Serilog.Sinks.File/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-uwV5hdhWPwUH1szhO8PJpFiahqXmzPzJT/sOijH/kFgUx+cyoDTMM8MHD0adw9+Iem6itoibbUXHYslzXsLEAg==", "path": "serilog.sinks.file/5.0.0", "hashPath": "serilog.sinks.file.5.0.0.nupkg.sha512"}, "SQLitePCLRaw.bundle_e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-ilkvNhrTersLmIVAcDwwPqfhUFCg19Z1GVMvCSi3xk6Akq94f4qadLORQCq/T8+9JgMiPs+F/NECw5uauviaNw==", "path": "sqlitepclraw.bundle_e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.bundle_e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.core/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-A8EBepVqY2lnAp3a8jnhbgzF2tlj2S3HcJQGANTYg/TbYbKa8Z5cM1h74An/vy0svhfzT7tVY0sFmUglLgv+2g==", "path": "sqlitepclraw.core/2.1.2", "hashPath": "sqlitepclraw.core.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.lib.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-zibGtku8M4Eea1R3ZCAxc86QbNvyEN17mAcQkvWKBuHvRpMiK2g5anG4R5Be7cWKSd1i6baYz8y4dMMAKcXKPg==", "path": "sqlitepclraw.lib.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.lib.e_sqlite3.2.1.2.nupkg.sha512"}, "SQLitePCLRaw.provider.e_sqlite3/2.1.2": {"type": "package", "serviceable": true, "sha512": "sha512-lxCZarZdvAsMl2zw9bXHrXK6RxVhB4b23iTFhCOdHFhxfbsxLxWf+ocvswJwR/9Wh/E//ddMi+wJGqUKV7VwoA==", "path": "sqlitepclraw.provider.e_sqlite3/2.1.2", "hashPath": "sqlitepclraw.provider.e_sqlite3.2.1.2.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-FK05XokgjgwlCI6wCT+D4/abtQkL1X1/B9Oas6uIwHFmYrIO9WUD5aLC9IzMs9GnHfUXOtXZ2S43gN1mhs5+aA==", "path": "swashbuckle.aspnetcore/6.5.0", "hashPath": "swashbuckle.aspnetcore.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-XWmCmqyFmoItXKFsQSwQbEAsjDKcxlNf1l+/Ki42hcb6LjKL8m5Db69OTvz5vLonMSRntYO1XLqz0OP+n3vKnA==", "path": "swashbuckle.aspnetcore.swagger/6.5.0", "hashPath": "swashbuckle.aspnetcore.swagger.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/qW8Qdg9OEs7V013tt+94OdPxbRdbhcEbw4NiwGvf4YBcfhL/y7qp/Mjv/cENsQ2L3NqJ2AOu94weBy/h4KvA==", "path": "swashbuckle.aspnetcore.swaggergen/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.5.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-OvbvxX+wL8skxTBttcBsVxdh73Fag4xwqEU2edh4JMn7Ws/xJHnY/JB1e9RoCb6XpDxUF3hD9A0Z1lEUx40Pfw==", "path": "swashbuckle.aspnetcore.swaggerui/6.5.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.5.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-/anOTeSZCNNI2zDilogWrZ8pNqCmYbzGNexUnNhjW8k0sHqEZ2nHJBp147jBV3hGYswu5lINpNg1vxR7bnqvVA==", "path": "system.configuration.configurationmanager/4.7.0", "hashPath": "system.configuration.configurationmanager.4.7.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.9.0": {"type": "package", "serviceable": true, "sha512": "sha512-7Pu9UjF1+so0s8zgzcIxSxbRQoiM2DMdwazVGmNptX3O6gDfMyeWZBd/Zn6VueDteN0ZTHw2acsf6+mAe8UpMw==", "path": "system.identitymodel.tokens.jwt/8.9.0", "hashPath": "system.identitymodel.tokens.jwt.8.9.0.nupkg.sha512"}, "System.Runtime.Caching/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-NdvNRjTPxYvIEhXQszT9L9vJhdQoX6AQ0AlhjTU+5NqFQVuacJTfhPVAvtGWNA2OJCqRiR/okBcZgMwI6MqcZg==", "path": "system.runtime.caching/4.7.0", "hashPath": "system.runtime.caching.4.7.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-ehYW0m9ptxpGWvE4zgqongBVWpSDU/JCFD4K7krxkQwSz/sFQjEXCUqpvencjy6DYDbn7Ig09R8GFffu8TtneQ==", "path": "system.security.cryptography.protecteddata/4.7.0", "hashPath": "system.security.cryptography.protecteddata.4.7.0.nupkg.sha512"}, "ShopApp.Application/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShopApp.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShopApp.Domain/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "ShopApp.Persistence/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}