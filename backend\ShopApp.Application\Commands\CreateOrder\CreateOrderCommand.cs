using MediatR;
using ShopApp.Application.DTOs;
using System.Collections.Generic;

namespace ShopApp.Application.Commands.CreateOrder;

public class CreateOrderCommand : IRequest<OrderDto>
{
    public string CustomerName { get; set; }
    public string CustomerEmail { get; set; }
    public string CustomerPhone { get; set; }
    public string ShippingAddress { get; set; }
    public List<CreateOrderItemDto> OrderItems { get; set; } = new List<CreateOrderItemDto>();
}
