using MediatR;
using ShopApp.Application.DTOs;
using System.Collections.Generic;

namespace ShopApp.Application.Queries.GetOrders;

public class GetOrdersQuery : IRequest<List<OrderDto>>
{
    public int Limit { get; set; } = 10;
    public string Sort { get; set; } = "createdAt";
    public string Order { get; set; } = "desc";
    public string? Status { get; set; }
    public string? UserId { get; set; }
}
