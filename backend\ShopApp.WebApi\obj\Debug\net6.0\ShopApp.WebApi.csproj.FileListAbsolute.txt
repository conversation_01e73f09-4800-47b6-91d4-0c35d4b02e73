C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\appsettings.Development.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\appsettings.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.WebApi.staticwebassets.endpoints.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.WebApi.exe
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.WebApi.deps.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.WebApi.runtimeconfig.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.WebApi.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.WebApi.pdb
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\AutoMapper.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\AutoMapper.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\FluentValidation.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\FluentValidation.DependencyInjectionExtensions.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Humanizer.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\MediatR.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\MediatR.Contracts.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\MediatR.Extensions.Microsoft.DependencyInjection.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Bcl.Memory.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Bcl.TimeProvider.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.SqlServer.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Extensions.Configuration.Binder.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Extensions.DependencyModel.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Identity.Client.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.IdentityModel.Abstractions.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.OpenApi.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Npgsql.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Serilog.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Serilog.Sinks.File.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\System.Configuration.ConfigurationManager.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\System.Runtime.Caching.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\unix\lib\netcoreapp3.1\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win\lib\netcoreapp3.1\Microsoft.Data.SqlClient.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win\lib\netstandard2.0\System.Runtime.Caching.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win\lib\netstandard2.0\System.Security.Cryptography.ProtectedData.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Application.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Core.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Domain.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Persistence.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Core.pdb
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Domain.pdb
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Application.pdb
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\ShopApp.Persistence.pdb
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.csproj.AssemblyReference.cache
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.AssemblyInfoInputs.cache
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.AssemblyInfo.cs
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\scopedcss\bundle\ShopApp.WebApi.styles.css
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\staticwebassets.build.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\staticwebassets.build.json.cache
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\staticwebassets.development.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\staticwebassets.build.endpoints.json
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp..348BBF32.Up2Date
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\refint\ShopApp.WebApi.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.pdb
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.genruntimeconfig.cache
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ref\ShopApp.WebApi.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.Data.Sqlite.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\Microsoft.EntityFrameworkCore.Sqlite.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\SQLitePCLRaw.batteries_v2.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\SQLitePCLRaw.core.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\SQLitePCLRaw.provider.e_sqlite3.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\alpine-arm\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\alpine-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\alpine-x64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\browser-wasm\nativeassets\net6.0\e_sqlite3.a
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-arm\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-armel\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-mips64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-musl-arm\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-musl-arm64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-musl-x64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-s390x\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-x64\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\linux-x86\native\libe_sqlite3.so
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\maccatalyst-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\maccatalyst-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\osx-arm64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\osx-x64\native\libe_sqlite3.dylib
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-arm\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-arm64\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-x64\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\bin\Debug\net6.0\runtimes\win-x86\native\e_sqlite3.dll
C:\Users\<USER>\Desktop\robinfull\backend\ShopApp.WebApi\obj\Debug\net6.0\ShopApp.WebApi.sourcelink.json
