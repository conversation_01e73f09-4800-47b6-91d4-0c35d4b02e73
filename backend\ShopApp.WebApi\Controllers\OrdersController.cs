using System;
using System.Security.Claims;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShopApp.Application.Commands.CreateOrder;
using ShopApp.Application.DTOs;
using ShopApp.Application.Queries.GetOrders;
using ShopApp.Application.Queries.GetOrderStats;
using ShopApp.Domain.Entities;

namespace ShopApp.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class OrdersController : ControllerBase
{
    private readonly IMediator _mediator;

    public OrdersController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Yeni sipariş oluşturur
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDto orderDto)
    {
        try
        {
            var command = new CreateOrderCommand
            {
                CustomerName = orderDto.CustomerName,
                CustomerEmail = orderDto.CustomerEmail,
                CustomerPhone = orderDto.CustomerPhone,
                ShippingAddress = orderDto.ShippingAddress,
                OrderItems = orderDto.OrderItems
            };

            var order = await _mediator.Send(command);
            return Ok(new { success = true, order });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Siparişleri listeler
    /// </summary>
    [HttpGet]
    [Authorize]
    public async Task<IActionResult> GetOrders([FromQuery] int limit = 10, [FromQuery] string sort = "createdAt", [FromQuery] string order = "desc")
    {
        try
        {
            var query = new GetOrdersQuery
            {
                Limit = limit,
                Sort = sort,
                Order = order
            };

            var orders = await _mediator.Send(query);

            return Ok(new {
                success = true,
                orders = orders,
                total = orders.Count,
                page = 1,
                totalPages = (int)Math.Ceiling((double)orders.Count / limit)
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Sipariş istatistiklerini getirir
    /// </summary>
    [HttpGet("stats")]
    [Authorize]
    public async Task<IActionResult> GetOrderStats()
    {
        try
        {
            var query = new GetOrderStatsQuery();
            var stats = await _mediator.Send(query);

            return Ok(new {
                success = true,
                stats = stats
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Kullanıcının siparişlerini getirir
    /// </summary>
    [HttpGet("myorders")]
    [Authorize]
    public async Task<IActionResult> GetMyOrders([FromQuery] int limit = 10, [FromQuery] string sort = "createdAt", [FromQuery] string order = "desc")
    {
        try
        {
            var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            if (string.IsNullOrEmpty(userId))
            {
                return Unauthorized(new { message = "Kullanıcı kimliği bulunamadı", success = false });
            }

            var query = new GetOrdersQuery
            {
                Limit = limit,
                Sort = sort,
                Order = order,
                UserId = userId
            };

            var orders = await _mediator.Send(query);

            return Ok(new {
                success = true,
                orders = orders,
                total = orders.Count,
                page = 1,
                totalPages = (int)Math.Ceiling((double)orders.Count / limit)
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Sipariş detayını getirir
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetOrder(Guid id)
    {
        try
        {
            // Bu endpoint'i daha sonra implement edeceğiz
            return Ok(new { success = true, message = "Sipariş detayları endpoint'i henüz implement edilmedi" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

}
