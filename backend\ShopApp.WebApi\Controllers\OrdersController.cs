using System;
using System.Security.Claims;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShopApp.Application.Commands.CreateOrder;
using ShopApp.Application.DTOs;
using ShopApp.Domain.Entities;

namespace ShopApp.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class OrdersController : ControllerBase
{
    private readonly IMediator _mediator;

    public OrdersController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Yeni sipariş oluşturur
    /// </summary>
    [HttpPost]
    public async Task<IActionResult> CreateOrder([FromBody] CreateOrderDto orderDto)
    {
        try
        {
            var command = new CreateOrderCommand
            {
                CustomerName = orderDto.CustomerName,
                CustomerEmail = orderDto.CustomerEmail,
                CustomerPhone = orderDto.CustomerPhone,
                ShippingAddress = orderDto.ShippingAddress,
                OrderItems = orderDto.OrderItems
            };

            var order = await _mediator.Send(command);
            return Ok(new { success = true, order });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Siparişleri listeler
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetOrders([FromQuery] int limit = 10, [FromQuery] string sort = "createdAt", [FromQuery] string order = "desc")
    {
        try
        {
            // Şimdilik boş liste döndür
            return Ok(new {
                success = true,
                orders = new List<object>(),
                total = 0,
                page = 1,
                totalPages = 0
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Sipariş istatistiklerini getirir
    /// </summary>
    [HttpGet("stats")]
    public async Task<IActionResult> GetOrderStats()
    {
        try
        {
            // Şimdilik mock data döndür
            return Ok(new {
                success = true,
                stats = new {
                    totalOrders = 0,
                    totalRevenue = 0,
                    pendingOrders = 0,
                    completedOrders = 0
                }
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Sipariş detayını getirir
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetOrder(Guid id)
    {
        try
        {
            // Bu endpoint'i daha sonra implement edeceğiz
            return Ok(new { success = true, message = "Sipariş detayları endpoint'i henüz implement edilmedi" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

}
