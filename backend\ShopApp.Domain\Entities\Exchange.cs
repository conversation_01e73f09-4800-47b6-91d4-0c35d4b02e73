using System;
using System.Collections.Generic;
using ShopApp.Core.Common;
using ShopApp.Core.Identity;

namespace ShopApp.Domain.Entities;

public class Exchange : BaseEntity
{
    public string Title { get; set; }
    public string Description { get; set; }
    public string Condition { get; set; }
    public decimal EstimatedValue { get; set; }
    public string Location { get; set; }
    public string City { get; set; }
    public string District { get; set; }
    public ExchangeStatus Status { get; set; } = ExchangeStatus.Active;
    public bool IsActive { get; set; } = true;

    // İlişkiler
    public Guid UserId { get; set; }
    public User User { get; set; }
    public Guid CategoryId { get; set; }
    public Category Category { get; set; }

    // Resimler
    public ICollection<ExchangeImage> Images { get; set; } = new List<ExchangeImage>();

    // Takas teklifleri
    public ICollection<ExchangeOffer> Offers { get; set; } = new List<ExchangeOffer>();
}

public class ExchangeImage : BaseEntity
{
    public Guid ExchangeId { get; set; }
    public Exchange Exchange { get; set; }
    public string ImageUrl { get; set; }
    public string ImageAlt { get; set; }
    public int DisplayOrder { get; set; } = 0;
    public bool IsPrimary { get; set; } = false;
}

public class ExchangeOffer : BaseEntity
{
    public Guid ExchangeId { get; set; }
    public Exchange Exchange { get; set; }
    public Guid OffererId { get; set; }
    public User Offerer { get; set; }
    public string OfferDescription { get; set; }
    public decimal OfferValue { get; set; }
    public ExchangeOfferStatus Status { get; set; } = ExchangeOfferStatus.Pending;
    public string? Notes { get; set; }
    public DateTime? ResponseDate { get; set; }
}

public enum ExchangeStatus
{
    Active = 0,
    Completed = 1,
    Cancelled = 2,
    Suspended = 3
}

public enum ExchangeOfferStatus
{
    Pending = 0,
    Accepted = 1,
    Rejected = 2,
    Cancelled = 3
}
