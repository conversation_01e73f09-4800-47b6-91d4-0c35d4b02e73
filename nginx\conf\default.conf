upstream robinhoot_app {
    server app:3000;
    # Ölçeklendirilmiş uygulama sunucuları için buraya daha fazla sunucu eklenebilir
    # server app2:3000;
    # server app3:3000;
}

# HTTP'den HTTPS'e yönlendirme
server {
    listen 80;
    server_name robinhoot.com www.robinhoot.com;
    
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS sunucusu
server {
    listen 443 ssl http2;
    server_name robinhoot.com www.robinhoot.com;
    
    # SSL sertifikaları
    ssl_certificate /etc/nginx/ssl/robinhoot.com.crt;
    ssl_certificate_key /etc/nginx/ssl/robinhoot.com.key;
    
    # SSL güvenlik ayarları
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:DHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384;
    
    # SSL oturum önbelleği
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    ssl_session_tickets off;
    
    # HSTS (HTTP Strict Transport Security)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Diğer güvenlik başlıkları
    add_header X-Content-Type-Options nosniff;
    add_header X-Frame-Options SAMEORIGIN;
    add_header X-XSS-Protection "1; mode=block";
    
    # Gzip sıkıştırma
    gzip on;
    gzip_vary on;
    gzip_min_length 1000;
    gzip_proxied any;
    gzip_types text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;
    
    # Önbelleğe alma ayarları
    proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=STATIC:10m inactive=7d use_temp_path=off;
    
    # Next.js uygulamasına proxy
    location / {
        proxy_pass http://robinhoot_app;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # Statik dosyalar için önbelleğe alma
    location /_next/static {
        proxy_pass http://robinhoot_app;
        proxy_cache STATIC;
        proxy_cache_valid 200 7d;
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
    }
    
    # Diğer statik dosyalar
    location /static {
        proxy_pass http://robinhoot_app;
        proxy_cache STATIC;
        proxy_cache_valid 200 7d;
        proxy_cache_use_stale error timeout http_500 http_502 http_503 http_504;
    }
    
    # Sağlık kontrolü
    location /health {
        access_log off;
        return 200 "OK";
    }
    
    # Hata sayfaları
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
} 