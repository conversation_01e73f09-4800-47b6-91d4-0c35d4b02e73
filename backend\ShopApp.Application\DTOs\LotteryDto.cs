using System;
using System.Collections.Generic;

namespace ShopApp.Application.DTOs;

public class LotteryDto
{
    public Guid Id { get; set; }
    public string Title { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Prize { get; set; } = string.Empty;
    public decimal TicketPrice { get; set; }
    public int MaxTickets { get; set; }
    public DateTime StartDate { get; set; }
    public DateTime EndDate { get; set; }
    public string Status { get; set; } = string.Empty;
    public string? PrizeImageUrl { get; set; }
    public string? PrizeImageAlt { get; set; }
    public DateTime? DrawDate { get; set; }
    public Guid? WinnerId { get; set; }
    public string? WinnerName { get; set; }
    public DateTime CreatedAt { get; set; }
    public DateTime UpdatedAt { get; set; }
    public int TicketsSold { get; set; }
    public int TicketsRemaining { get; set; }
    public List<LotteryTicketDto> Tickets { get; set; } = new List<LotteryTicketDto>();
}

public class LotteryTicketDto
{
    public Guid Id { get; set; }
    public Guid LotteryId { get; set; }
    public Guid UserId { get; set; }
    public string UserName { get; set; } = string.Empty;
    public string TicketNumber { get; set; } = string.Empty;
    public decimal PaidAmount { get; set; }
    public bool IsWinner { get; set; }
    public DateTime CreatedAt { get; set; }
}
