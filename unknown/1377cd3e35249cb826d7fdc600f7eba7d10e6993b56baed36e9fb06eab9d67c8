{"name": "<PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-icons": "^1.3.0", "@types/react-datepicker": "^6.2.0", "@upstash/redis": "^1.34.9", "axios": "^1.9.0", "clsx": "^2.1.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "mongodb": "^6.16.0", "mongoose": "^8.14.1", "next": "15.3.2", "next-themes": "^0.2.1", "react": "^19.0.0", "react-datepicker": "^8.3.0", "react-dom": "^19.0.0", "react-icons": "^5.5.0", "tailwind-merge": "^2.3.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}