﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\6.0.4\buildTransitive\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.logging.abstractions\6.0.4\buildTransitive\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.bcl.memory\9.0.0\buildTransitive\netcoreapp2.0\Microsoft.Bcl.Memory.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.bcl.memory\9.0.0\buildTransitive\netcoreapp2.0\Microsoft.Bcl.Memory.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.primitives\9.0.4\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Primitives.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.primitives\9.0.4\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Primitives.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.abstractions\9.0.4\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Abstractions.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.abstractions\9.0.4\buildTransitive\netcoreapp2.0\Microsoft.Extensions.Configuration.Abstractions.targets')" />
    <Import Project="$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.4\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets" Condition="Exists('$(NuGetPackageRoot)microsoft.extensions.configuration.binder\9.0.4\buildTransitive\netstandard2.0\Microsoft.Extensions.Configuration.Binder.targets')" />
  </ImportGroup>
</Project>