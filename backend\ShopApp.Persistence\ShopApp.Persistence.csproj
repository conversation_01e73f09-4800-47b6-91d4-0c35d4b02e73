﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <ProjectReference Include="..\ShopApp.Core\ShopApp.Core.csproj" />
    <ProjectReference Include="..\ShopApp.Domain\ShopApp.Domain.csproj" />
    <ProjectReference Include="..\ShopApp.Application\ShopApp.Application.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.36" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="6.0.29" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
