using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using ShopApp.Application.Commands.CreateProduct;
using ShopApp.Application.Commands.UpdateProduct;
using ShopApp.Application.DTOs;
using ShopApp.Application.Queries.GetProducts;

namespace ShopApp.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ProductsController : ControllerBase
{
    private readonly IMediator _mediator;

    public ProductsController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<List<ProductDto>>> GetAll([FromQuery] bool? hasDynamicPricing = null, [FromQuery] int? limit = null)
    {
        var products = await _mediator.Send(new GetProductsQuery());

        // Dinamik fiyatlandırma filtresi
        if (hasDynamicPricing.HasValue && hasDynamicPricing.Value)
        {
            // Dinamik fiyatlandırma aktif olan ürünleri filtrele
            products = products.Where(p => p.IsActive && p.HasDynamicPricing).ToList();

            // Dinamik fiyatları hesapla
            foreach (var product in products)
            {
                product.CurrentPrice = CalculateDynamicPrice(product);
            }
        }

        // Limit filtresi
        if (limit.HasValue && limit.Value > 0)
        {
            products = products.Take(limit.Value).ToList();
        }

        return products;
    }

    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<ProductDto>> GetById(Guid id)
    {
        // Bu kısmı ileride GetProductByIdQuery ile değiştirebilirsiniz
        var products = await _mediator.Send(new GetProductsQuery());
        var product = products.Find(p => p.Id == id);

        if (product == null)
        {
            return NotFound();
        }

        return product;
    }

    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ProductDto>> Create(CreateProductCommand command)
    {
        try
        {
            var product = await _mediator.Send(command);
            return CreatedAtAction(nameof(GetById), new { id = product.Id }, product);
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<ProductDto>> Update(Guid id, UpdateProductCommand command)
    {
        if (id != command.Id)
        {
            return BadRequest(new { message = "ID mismatch", success = false });
        }

        try
        {
            var product = await _mediator.Send(command);
            return Ok(product);
        }
        catch (Exception ex)
        {
            return NotFound(new { message = ex.Message, success = false });
        }
    }

    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> Delete(Guid id)
    {
        try
        {
            // Basit silme işlemi - ileride DeleteProductCommand eklenebilir
            // Şimdilik sadece 404 döndürelim
            return NotFound(new { message = "Delete operation not implemented yet", success = false });
        }
        catch (Exception ex)
        {
            return NotFound(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Ürün için dinamik fiyat hesaplar
    /// </summary>
    [HttpGet("{id}/dynamic-price")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<object>> GetDynamicPrice(Guid id, [FromQuery] int quantity = 1)
    {
        try
        {
            var products = await _mediator.Send(new GetProductsQuery());
            var product = products.Find(p => p.Id == id);

            if (product == null)
            {
                return NotFound(new { message = "Ürün bulunamadı", success = false });
            }

            if (!product.HasDynamicPricing)
            {
                return Ok(new {
                    success = true,
                    hasDynamicPricing = false,
                    basePrice = product.Price,
                    currentPrice = product.Price,
                    quantity = quantity,
                    totalPrice = product.Price * quantity
                });
            }

            var dynamicPrice = CalculateDynamicPrice(product, quantity);

            return Ok(new {
                success = true,
                hasDynamicPricing = true,
                basePrice = product.Price,
                currentPrice = dynamicPrice,
                quantity = quantity,
                totalPrice = dynamicPrice * quantity,
                discountPercentage = ((product.Price - dynamicPrice) / product.Price) * 100
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Dinamik fiyat hesaplama metodu
    /// </summary>
    private decimal CalculateDynamicPrice(ProductDto product, int quantity = 1)
    {
        if (!product.HasDynamicPricing)
            return product.Price;

        var basePrice = product.Price;
        var discountPercentage = 0m;

        // Stok bazlı indirim (stok azaldıkça fiyat artar)
        if (product.Stock > 0)
        {
            var stockRatio = (decimal)product.Stock / 100; // Varsayılan başlangıç stoku 100
            if (stockRatio < 0.2m) // %20'den az stok kaldıysa
            {
                discountPercentage = 0; // İndirim yok, normal fiyat
            }
            else if (stockRatio < 0.5m) // %50'den az stok
            {
                discountPercentage = 5; // %5 indirim
            }
            else if (stockRatio < 0.8m) // %80'den az stok
            {
                discountPercentage = 10; // %10 indirim
            }
            else
            {
                discountPercentage = 15; // %15 indirim
            }
        }

        // Miktar bazlı indirim
        if (quantity >= 10)
        {
            discountPercentage += 5; // Ek %5 indirim
        }
        else if (quantity >= 5)
        {
            discountPercentage += 3; // Ek %3 indirim
        }

        // Maksimum indirim %30
        discountPercentage = Math.Min(discountPercentage, 30);

        var discountedPrice = basePrice * (1 - discountPercentage / 100);
        return Math.Max(discountedPrice, basePrice * 0.5m); // Minimum %50 fiyat
    }
}