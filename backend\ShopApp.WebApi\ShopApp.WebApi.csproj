<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="6.0.36" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="6.0.36">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ShopApp.Core\ShopApp.Core.csproj" />
    <ProjectReference Include="..\ShopApp.Domain\ShopApp.Domain.csproj" />
    <ProjectReference Include="..\ShopApp.Application\ShopApp.Application.csproj" />
    <ProjectReference Include="..\ShopApp.Persistence\ShopApp.Persistence.csproj" />
  </ItemGroup>

</Project>
