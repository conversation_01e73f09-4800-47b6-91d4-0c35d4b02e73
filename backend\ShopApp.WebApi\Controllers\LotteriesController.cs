using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ShopApp.Application.Queries.GetLotteries;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace ShopApp.WebApi.Controllers;

[ApiController]
[Route("api/[controller]")]
public class LotteriesController : ControllerBase
{
    private readonly IMediator _mediator;

    public LotteriesController(IMediator mediator)
    {
        _mediator = mediator;
    }

    /// <summary>
    /// Çekilişleri listeler
    /// </summary>
    [HttpGet]
    public async Task<IActionResult> GetLotteries([FromQuery] string status = "all", [FromQuery] int limit = 10)
    {
        try
        {
            var query = new GetLotteriesQuery
            {
                Status = status,
                Limit = limit
            };

            var lotteries = await _mediator.Send(query);

            return Ok(new {
                success = true,
                lotteries = lotteries,
                total = lotteries.Count
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Çekiliş detayını getirir
    /// </summary>
    [HttpGet("{id}")]
    public async Task<IActionResult> GetLottery(Guid id)
    {
        try
        {
            return Ok(new { success = true, message = "Çekiliş detayları endpoint'i henüz implement edilmedi" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }

    /// <summary>
    /// Çekiliş siler
    /// </summary>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteLottery(Guid id)
    {
        try
        {
            return Ok(new { success = true, message = "Çekiliş silme endpoint'i henüz implement edilmedi" });
        }
        catch (Exception ex)
        {
            return BadRequest(new { message = ex.Message, success = false });
        }
    }
}
