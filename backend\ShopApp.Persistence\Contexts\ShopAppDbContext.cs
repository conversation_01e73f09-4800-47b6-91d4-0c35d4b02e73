using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using ShopApp.Application.Interfaces;
using ShopApp.Core.Identity;
using ShopApp.Domain.Entities;

namespace ShopApp.Persistence.Contexts;

public class ShopAppDbContext : DbContext, IApplicationDbContext
{
    public ShopAppDbContext(DbContextOptions<ShopAppDbContext> options)
        : base(options)
    {
    }

    public DbSet<Product> Products => Set<Product>();
    public DbSet<Category> Categories => Set<Category>();
    public DbSet<Cart> Carts => Set<Cart>();
    public DbSet<CartItem> CartItems => Set<CartItem>();

    // Discount DbSet'leri
    public DbSet<Discount> Discounts => Set<Discount>();
    public DbSet<DiscountRule> DiscountRules => Set<DiscountRule>();

    // Auction DbSet'leri
    public DbSet<Auction> Auctions => Set<Auction>();
    public DbSet<AuctionBid> AuctionBids => Set<AuctionBid>();

    // User DbSet'leri
    public DbSet<User> Users => Set<User>();
    public DbSet<ApplicationUser> ApplicationUsers => Set<ApplicationUser>();

    // Müşteri DbSet'leri
    public DbSet<BireyselMusteri> BireyselMusteriler => Set<BireyselMusteri>();
    public DbSet<KurumsalMusteri> KurumsalMusteriler => Set<KurumsalMusteri>();

    // Order DbSet'leri
    public DbSet<Order> Orders => Set<Order>();
    public DbSet<OrderItem> OrderItems => Set<OrderItem>();

    // Site Settings
    public DbSet<SiteSetting> SiteSettings => Set<SiteSetting>();

    // Bid DbSet'i
    public DbSet<Bid> Bids => Set<Bid>();

    // Lottery DbSet'leri
    public DbSet<Lottery> Lotteries => Set<Lottery>();
    public DbSet<LotteryTicket> LotteryTickets => Set<LotteryTicket>();

    // Banner DbSet'i
    public DbSet<Banner> Banners => Set<Banner>();

    // Exchange DbSet'leri
    public DbSet<Exchange> Exchanges => Set<Exchange>();
    public DbSet<ExchangeImage> ExchangeImages => Set<ExchangeImage>();
    public DbSet<ExchangeOffer> ExchangeOffers => Set<ExchangeOffer>();

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await base.SaveChangesAsync(cancellationToken);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Entity Configuration'ları burada uygulanır
        modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

        // User ve ApplicationUser için farklı tablolar
        modelBuilder.Entity<User>().ToTable("Users");
        modelBuilder.Entity<ApplicationUser>().ToTable("ApplicationUsers");

        base.OnModelCreating(modelBuilder);
    }
}
