using MediatR;

namespace ShopApp.Application.Queries.GetOrderStats;

public class GetOrderStatsQuery : IRequest<OrderStatsDto>
{
}

public class OrderStatsDto
{
    public int TotalOrders { get; set; }
    public decimal TotalRevenue { get; set; }
    public int PendingOrders { get; set; }
    public int CompletedOrders { get; set; }
    public int ProcessingOrders { get; set; }
    public int CancelledOrders { get; set; }
    public decimal MonthlyRevenue { get; set; }
    public int MonthlyOrders { get; set; }
}
