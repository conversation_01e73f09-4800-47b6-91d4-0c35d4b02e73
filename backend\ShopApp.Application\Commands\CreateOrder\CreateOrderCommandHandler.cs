using MediatR;
using Microsoft.EntityFrameworkCore;
using ShopApp.Application.DTOs;
using ShopApp.Application.Interfaces;
using ShopApp.Domain.Entities;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ShopApp.Application.Commands.CreateOrder;

public class CreateOrderCommandHandler : IRequestHandler<CreateOrderCommand, OrderDto>
{
    private readonly IApplicationDbContext _context;

    public CreateOrderCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<OrderDto> Handle(CreateOrderCommand request, CancellationToken cancellationToken)
    {
        // Validasyonlar
        if (string.IsNullOrEmpty(request.CustomerName))
            throw new Exception("Müşteri adı gereklidir");

        if (string.IsNullOrEmpty(request.CustomerEmail))
            throw new Exception("E-posta adresi gereklidir");

        if (string.IsNullOrEmpty(request.CustomerPhone))
            throw new Exception("Telefon numarası gereklidir");

        if (string.IsNullOrEmpty(request.ShippingAddress))
            throw new Exception("Teslimat adresi gereklidir");

        if (!request.OrderItems.Any())
            throw new Exception("Sipariş öğeleri gereklidir");

        // Toplam tutarı hesapla
        decimal itemsPrice = request.OrderItems.Sum(item => item.Price * item.Quantity);
        decimal taxPrice = itemsPrice * 0.18m; // %18 KDV
        decimal shippingPrice = 29.99m; // Sabit kargo ücreti
        decimal totalPrice = itemsPrice + taxPrice + shippingPrice;

        // Sipariş oluştur
        var order = new Order
        {
            Id = Guid.NewGuid(),
            OrderNumber = $"ORD-{DateTime.Now:yyyyMMdd}-{Guid.NewGuid().ToString("N")[..8].ToUpper()}",
            ShippingName = request.CustomerName,
            ShippingAddress = request.ShippingAddress,
            ShippingPhone = request.CustomerPhone,
            ItemsPrice = itemsPrice,
            TaxPrice = taxPrice,
            ShippingPrice = shippingPrice,
            TotalPrice = totalPrice,
            Status = OrderStatus.Pending,
            PaymentMethod = PaymentMethod.CreditCard,
            PaymentStatus = PaymentStatus.Pending,
            CreatedDate = DateTime.UtcNow
        };

        _context.Orders.Add(order);

        // Sipariş öğelerini oluştur
        foreach (var item in request.OrderItems)
        {
            var orderItem = new OrderItem
            {
                Id = Guid.NewGuid(),
                OrderId = order.Id,
                ProductId = Guid.Parse(item.Product),
                ProductName = item.Name,
                Quantity = item.Quantity,
                UnitPrice = item.Price,
                TotalPrice = item.Price * item.Quantity,
                CreatedDate = DateTime.UtcNow
            };

            _context.OrderItems.Add(orderItem);
        }

        await _context.SaveChangesAsync(cancellationToken);

        // DTO'ya çevir
        return new OrderDto
        {
            Id = order.Id,
            OrderNumber = order.OrderNumber,
            Status = order.Status,
            StatusText = order.Status.ToString(),
            ItemsPrice = order.ItemsPrice,
            TaxPrice = order.TaxPrice,
            ShippingPrice = order.ShippingPrice,
            TotalPrice = order.TotalPrice,
            Shipping = new ShippingInfoDto
            {
                Name = order.ShippingName,
                Address = order.ShippingAddress,
                Phone = order.ShippingPhone
            },
            Payment = new PaymentInfoDto
            {
                Method = order.PaymentMethod,
                MethodText = order.PaymentMethod.ToString(),
                Status = order.PaymentStatus,
                StatusText = order.PaymentStatus.ToString()
            },
            CreatedAt = order.CreatedDate,
            OrderItems = request.OrderItems.Select(item => new OrderItemDto
            {
                Id = Guid.NewGuid(),
                ProductId = Guid.Parse(item.Product),
                ProductName = item.Name,
                Quantity = item.Quantity,
                UnitPrice = item.Price,
                TotalPrice = item.Price * item.Quantity
            }).ToList()
        };
    }
}
