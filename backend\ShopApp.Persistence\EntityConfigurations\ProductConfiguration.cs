using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using ShopApp.Domain.Entities;

namespace ShopApp.Persistence.EntityConfigurations;

public class ProductConfiguration : IEntityTypeConfiguration<Product>
{
    public void Configure(EntityTypeBuilder<Product> builder)
    {
        builder.ToTable("Products");
        
        builder.Property(p => p.Name)
            .IsRequired()
            .HasMaxLength(200);
            
        builder.Property(p => p.Description)
            .HasMaxLength(1000);
            
        builder.Property(p => p.Price)
            .HasColumnType("decimal(18,2)");
            
        builder.Property(p => p.IsActive)
            .HasDefaultValue(true);
            
        builder.Property(p => p.ImageUrl)
            .HasDefaultValue("https://picsum.photos/400/300");
            
        // Add missing properties
        builder.Property(p => p.HasDynamicPricing)
            .HasDefaultValue(false);
            
        builder.Property(p => p.OriginalPrice)
            .HasColumnType("decimal(18,2)");
            
        builder.Property(p => p.MaxDiscountPercentage);
            
        builder.Property(p => p.IsDynamicPricingActive)
            .HasDefaultValue(false);
    }
}