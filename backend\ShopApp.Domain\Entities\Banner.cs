using System;
using ShopApp.Core.Common;

namespace ShopApp.Domain.Entities;

public class Banner : BaseEntity
{
    public string Title { get; set; }
    public string Description { get; set; }
    public string ImageUrl { get; set; }
    public string ImageAlt { get; set; }
    public string? LinkUrl { get; set; }
    public string? LinkText { get; set; }
    public BannerType Type { get; set; } = BannerType.Hero;
    public BannerPosition Position { get; set; } = BannerPosition.Top;
    public int DisplayOrder { get; set; } = 0;
    public bool IsActive { get; set; } = true;
    public DateTime? StartDate { get; set; }
    public DateTime? EndDate { get; set; }
    public string? BackgroundColor { get; set; }
    public string? TextColor { get; set; }
}

public enum BannerType
{
    Hero = 0,
    Promotional = 1,
    Category = 2,
    Product = 3,
    Announcement = 4
}

public enum BannerPosition
{
    Top = 0,
    Middle = 1,
    Bottom = 2,
    Sidebar = 3,
    Header = 4,
    Footer = 5
}
