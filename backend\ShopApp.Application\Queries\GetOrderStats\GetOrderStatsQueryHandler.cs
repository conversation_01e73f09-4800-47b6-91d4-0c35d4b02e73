using MediatR;
using Microsoft.EntityFrameworkCore;
using ShopApp.Application.Interfaces;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ShopApp.Application.Queries.GetOrderStats;

public class GetOrderStatsQueryHandler : IRequestHandler<GetOrderStatsQuery, OrderStatsDto>
{
    private readonly IApplicationDbContext _context;

    public GetOrderStatsQueryHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<OrderStatsDto> Handle(GetOrderStatsQuery request, CancellationToken cancellationToken)
    {
        try
        {
            var orders = await _context.Orders.ToListAsync(cancellationToken);
            var currentMonth = DateTime.Now.Month;
            var currentYear = DateTime.Now.Year;

            var monthlyOrders = orders.Where(o => o.CreatedDate.Month == currentMonth && o.CreatedDate.Year == currentYear).ToList();

            var stats = new OrderStatsDto
            {
                TotalOrders = orders.Count,
                TotalRevenue = orders.Sum(o => o.TotalPrice),
                PendingOrders = orders.Count(o => o.Status == Domain.Entities.OrderStatus.Pending),
                CompletedOrders = orders.Count(o => o.Status == Domain.Entities.OrderStatus.Completed),
                ProcessingOrders = orders.Count(o => o.Status == Domain.Entities.OrderStatus.Processing),
                CancelledOrders = orders.Count(o => o.Status == Domain.Entities.OrderStatus.Cancelled),
                MonthlyOrders = monthlyOrders.Count,
                MonthlyRevenue = monthlyOrders.Sum(o => o.TotalPrice)
            };

            return stats;
        }
        catch (Exception)
        {
            // Return default stats if there's an error
            return new OrderStatsDto
            {
                TotalOrders = 0,
                TotalRevenue = 0,
                PendingOrders = 0,
                CompletedOrders = 0,
                ProcessingOrders = 0,
                CancelledOrders = 0,
                MonthlyOrders = 0,
                MonthlyRevenue = 0
            };
        }
    }
}
