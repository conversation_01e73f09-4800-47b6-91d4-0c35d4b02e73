using System.Threading;
using System.Threading.Tasks;
using MediatR;
using ShopApp.Application.DTOs;
using ShopApp.Application.Interfaces;
using ShopApp.Core.Interfaces;
using ShopApp.Domain.Entities;

namespace ShopApp.Application.Commands.CreateProduct;

public class CreateProductCommandHandler : IRequestHandler<CreateProductCommand, ProductDto>
{
    private readonly IApplicationDbContext _context;

    public CreateProductCommandHandler(IApplicationDbContext context)
    {
        _context = context;
    }

    public async Task<ProductDto> Handle(CreateProductCommand request, CancellationToken cancellationToken)
    {
        var product = new Product
        {
            Name = request.Name,
            Description = request.Description,
            Price = request.Price,
            StockQuantity = request.StockQuantity,
            IsActive = request.IsActive,
            ImageUrl = request.ImageUrl ?? "https://picsum.photos/400/300",
            ImageAlt = request.ImageAlt ?? request.Name,
            CategoryId = request.CategoryId
        };

        await _context.Products.AddAsync(product, cancellationToken);
        await _context.SaveChangesAsync(cancellationToken);

        // Kategori bilgisini almak için Category'yi yüklemek gerekecek
        // Bu örnek için basit bir DTO dönüşü yapıyoruz
        return new ProductDto
        {
            Id = product.Id,
            Name = product.Name,
            Description = product.Description,
            Price = product.Price,
            StockQuantity = product.StockQuantity,
            IsActive = product.IsActive,
            ImageUrl = product.ImageUrl,
            ImageAlt = product.ImageAlt,
            CategoryId = product.CategoryId,
            CreatedDate = product.CreatedDate,
            ModifiedDate = product.ModifiedDate
        };
    }
}