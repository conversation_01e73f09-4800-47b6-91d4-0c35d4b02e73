using MediatR;
using Microsoft.EntityFrameworkCore;
using ShopApp.Application.DTOs;
using ShopApp.Application.Interfaces;
using ShopApp.Domain.Entities;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ShopApp.Application.Commands.CreateBid;

public class CreateBidCommandHandler : IRequestHandler<CreateBidCommand, BidDto>
{
    private readonly IApplicationDbContext _context;
    private readonly IServiceProvider _serviceProvider;

    public CreateBidCommandHandler(IApplicationDbContext context, IServiceProvider serviceProvider)
    {
        _context = context;
        _serviceProvider = serviceProvider;
    }

    public async Task<BidDto> Handle(CreateBidCommand request, CancellationToken cancellationToken)
    {
        // Açık artırmayı kontrol et
        var auction = await _context.Auctions
            .Include(a => a.Bids)
            .Include(a => a.HighestBidder)
            .FirstOrDefaultAsync(a => a.Id == request.AuctionId, cancellationToken);

        if (auction == null)
            throw new Exception("Açık artırma bulunamadı");

        if (auction.Status != AuctionStatus.Active)
            throw new Exception("Bu açık artırma aktif değil");

        if (auction.EndTime <= DateTime.UtcNow)
            throw new Exception("Bu açık artırma sona ermiş");

        // Minimum teklif kontrolü (mevcut fiyat + minimum artış miktarı)
        var minimumBid = auction.CurrentPrice + auction.MinIncrement;
        if (request.Amount < minimumBid)
            throw new Exception($"Teklif en az {minimumBid:C} olmalıdır");

        // Kullanıcıyı kontrol et
        var user = await _context.ApplicationUsers.FirstOrDefaultAsync(u => u.Id == request.UserId, cancellationToken);
        if (user == null)
            throw new Exception("Kullanıcı bulunamadı");

        // Önceki tüm teklifleri kazanmayan olarak işaretle
        var previousBids = auction.Bids.Where(b => b.IsWinning);
        foreach (var prevBid in previousBids)
        {
            prevBid.IsWinning = false;
        }

        // Yeni teklif oluştur
        var bid = new Bid
        {
            Id = Guid.NewGuid(),
            AuctionId = request.AuctionId,
            UserId = request.UserId,
            Amount = request.Amount,
            BidTime = DateTime.UtcNow,
            IsWinning = true,
            Notes = request.Notes,
            CreatedDate = DateTime.UtcNow
        };

        _context.Bids.Add(bid);

        // Açık artırmayı güncelle
        auction.CurrentPrice = request.Amount;
        auction.HighestBidderId = request.UserId;
        auction.ModifiedDate = DateTime.UtcNow;

        await _context.SaveChangesAsync(cancellationToken);

        var bidDto = new BidDto
        {
            Id = bid.Id,
            AuctionId = bid.AuctionId,
            UserId = bid.UserId,
            User = new UserDto
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email
            },
            Amount = bid.Amount,
            BidTime = bid.BidTime,
            IsWinning = bid.IsWinning,
            Notes = bid.Notes
        };

        // SignalR üzerinden gerçek zamanlı güncelleme gönder
        var hubContext = scope.ServiceProvider.GetRequiredService<IHubContext<AuctionHub>>();
        await hubContext.Clients.Group(auction.Id.ToString()).SendAsync("NewBid", bidDto);

        return bidDto;
    }
}
